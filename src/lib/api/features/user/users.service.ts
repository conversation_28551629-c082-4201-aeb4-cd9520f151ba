// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';

import type { UserResponse, LoginTokenResponse, UpdatedLineUserResponse } from "../../types/user";
import type { TicketResponse } from "../../types/ticket";
import { ApiError } from "../../client/errors";

export class UserService {
    private baseUrl = `${getBackendUrl()}/user`;

    async getAll(token: string): Promise<UserResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/users-infos/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                users: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching users:', error);
            return {
                users: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch users'
            };
        }
    }

    async getById(id: string, token: string): Promise<UserResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/users-infos/${id}/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                users: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching user:', error);
            return {
                users: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch user'
            };
        }
    }

    async getUserInfo(token: string): Promise<UserResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/users/me/infos/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                users: response_json,
                res_status: res_status
            };
        } catch (error) {
            console.error('Error fetching user:', error);
            return {
                users: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch user'
            };
        }
    }

    async putById(id: string, userData, token: string): Promise<UserResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/user/${id}/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            console.info(`user's putById method - ${response_json.message}`);
            return {
                users: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };

        } catch (error) {
            console.error('Error updating user:', error);
            return {
                users: [],
                res_status: error.status,
                error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to updating user'
            };
        }
    }

    async deleteById(id: string, userData, token: string): Promise<UserResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/user/${id}/`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            console.log(`Delete's response - ${JSON.stringify(response)}`)
            console.log(`Delete's status - ${JSON.stringify(response.status)}`)
            console.log(`Delete's response_json - ${JSON.stringify(response_json)}`)

            console.info(`deleteById method - ${response_json.message}`);
            return {
                users: [],
                res_status: res_status,
                res_msg: response_json.message
            };

        } catch (error) {
            console.error('Error deleting user asdasddaasd:', error);
            return {
                users: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to deleting user'
            };
        }
    }

    async signUpNewUser(newUserData, token: string): Promise<UserResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/signup/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(newUserData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                users: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };

        } catch (error) {
            console.error('Error sign-up user:', error);
            return {
                users: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to sign-up user'
            };
        }
    }

    async assignRoleById(id: string, roleData: { role_ids: (string | number)[] }, token: string): Promise<UserResponse> {
        try {
            // const response = await fetch(`${this.baseUrl}/api/users/${id}/roles/assign/`, {
            const response = await fetch(`${this.baseUrl}/api/users/${id}/roles/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(roleData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            console.info(`user's assignRoleById method - ${response_json.message}`);
            return {
                users: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };

        } catch (error) {
            console.error('Error updating user:', error);
            return {
                users: [],
                res_status: error.status,
                error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to updating user'
            };
        }
    }

    async assignPartnerById(id: string, partnerData: { partner_ids: (string | number)[] }, token: string): Promise<UserResponse> {
        try {
            console.log(`assignPartnerById data: ${JSON.stringify(partnerData)}`);
    
            const response = await fetch(`${this.baseUrl}/api/users/${id}/partners/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(partnerData)
            });
    
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }
    
            const response_json = await response.json();
            const res_status = response.status;
    
            console.info(`assignPartnerById method - ${response_json.message}`);
            return {
                users: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };
    
        } catch (error) {
            console.error('Error assigning partner:', error);
            return {
                users: [],
                res_status: error.status,
                error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to assign partner'
            };
        }
    }

    async assignDepartmentById(userId: string, departmentData: { department_ids: (string | number)[] }, token: string): Promise<UserResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/users/${userId}/departments/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(departmentData)
            });
    
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }
    
            const response_json = await response.json();
            const res_status = response.status;
    
            console.info(`assignDepartmentById method - ${response_json.message}`);
            return {
                users: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };
    
        } catch (error) {
            console.error('Error assigning departments to user:', error);
            return {
                users: [],
                res_status: error.status,
                error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to update user departments'
            };
        }
    }

    async assignCompanyById(id: string, partnerData: { company_ids: (string | number)[] }, token: string): Promise<UserResponse> {
        try {
            console.log(`assignCompanyById's partnerData - ${JSON.stringify(partnerData)}`)

            const response = await fetch(`${this.baseUrl}/api/users/${id}/partners/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(partnerData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            console.info(`user's assignCompanyById method - ${response_json.message}`);
            return {
                users: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };

        } catch (error) {
            console.error('Error updating user:', error);
            return {
                users: [],
                res_status: error.status,
                error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to updating user'
            };
        }
    }

    async removeRoleById(id: string, roleData: { role_ids: (string | number)[] }, token: string): Promise<UserResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/users/${id}/roles/remove/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(roleData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            console.info(`user's removeRoleById method - ${response_json.message}`);
            return {
                users: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };

        } catch (error) {
            console.error('Error updating user:', error);
            return {
                users: [],
                res_status: error.status,
                error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to updating user'
            };
        }
    }

    async getUserTickets(id: string, token: string): Promise<UserResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/users/${id}/tickets/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();

            // TODO - Delete this
            // console.log(`getUserTickets's response_json - ${JSON.stringify(response_json)}`)

            const res_status = response.status;

            return {
                users: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching user:', error);
            return {
                users: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch user'
            };
        }
    }

    async 

    async refreshToken(refresh_token: string): Promise<LoginTokenResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/jwt/refresh/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ "refresh": refresh_token })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }


            const response_json = await response.json();
            const res_status = response.status;

            return {
                login_token: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching users:', error);
            return {
                login_token: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Invalid refresh token!'
            };
        }
    }

    async updateUserLineAccountId(userId: string, lineIdData: { line_user_id: string }, token: string): Promise<UpdatedLineUserResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/users/${userId}/line-account/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'X-API-Key': token, // Added API Key header as per curl request
                },
                body: JSON.stringify(lineIdData),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseJson = await response.json();

            console.log("Update User Line Account Success");

            return {
                data: responseJson,
                res_status: response.status,
                res_msg: responseJson.message || "User Line Account Updated Successfully",
            };

        } catch (error) {
            console.error('Error updating user line account:', error);

            return {
                data: [],
                res_status: error instanceof ApiError ? error.status : 500,
                error_msg: error instanceof Error ? `Error message - ${error.message}` : 'Failed to update user line id'
            };
        }
    }

    // ========================= Department Methods =========================
    // GET /user/api/department/
    async getAllDepartment(token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/department/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(JSON.stringify(errorData), response.status);
            }
            const response_json = await response.json();
            const res_status = response.status;
            return {
                departments: response_json,
                res_status: res_status
            };
        } catch (error) {
            console.error('Error fetching departments:', error);
            return {
                departments: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch departments'
            };
        }
    }

    // POST /user/api/department/
    // Example JSON body:
    // {
    //   "name": "Non-Motor Insurance-Motor Product Department Manager",
    //   "code": "NMIMPDM",
    //   "description": "Created Non-Motor Insurance-Motor Product Department Manager department description",
    //   "is_active": true
    // }
    async createDepartment(departmentData: any, token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/department/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(departmentData)
            });
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(JSON.stringify(errorData), response.status);
            }
            const response_json = await response.json();
            const res_status = response.status;
            return {
                department: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };
        } catch (error) {
            console.error('Error creating department:', error);
            return {
                department: null,
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to create department'
            };
        }
    }

    // DELETE /user/api/department/{{backend_department_id}}/
    async deleteDepartment(departmentId: string, token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/department/${departmentId}/`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(JSON.stringify(errorData), response.status);
            }
            const response_json = await response.json();
            const res_status = response.status;
            return {
                res_status: res_status,
                res_msg: response_json.message
            };
        } catch (error) {
            console.error('Error deleting department:', error);
            return {
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to delete department'
            };
        }
    }

    // PUT /user/api/department/{{backend_department_id}}/
    // Example JSON body:
    // {
    //   "name": "Non-Motor Insurance-Motor Product Department Manager",
    //   "code": "NMIMPDM",
    //   "description": "Updated Non-Motor Insurance-Motor Product Department Manager department description",
    //   "is_active": true
    // }
    async updateDepartment(departmentId: string, departmentData: any, token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/department/${departmentId}/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(departmentData)
            });
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(JSON.stringify(errorData), response.status);
            }
            const response_json = await response.json();
            const res_status = response.status;
            return {
                department: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };
        } catch (error) {
            console.error('Error updating department:', error);
            return {
                department: null,
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to update department'
            };
        }
    }

    // ========================= Tag Methods =========================
    // GET /user/api/user-tag/
    async getAllTags(token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/user-tag/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(JSON.stringify(errorData), response.status);
            }
            const response_json = await response.json();
            const res_status = response.status;

            return {
                tags: response_json,
                res_status: res_status
            };
        } catch (error) {
            console.error('Error fetching departments:', error);
            return {
                tags: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch tags'
            };
        }
    }

    async createTag(tagData: { name: string }, token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/user-tag/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(tagData)
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(JSON.stringify(errorData), response.status);
            }
            
            const response_json = await response.json();
            const res_status = response.status;
            
            return {
                tag: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };
        } catch (error) {
            console.error('Error creating tag:', error);
            return {
                tag: null,
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to create tag'
            };
        }
    }
    
    async deleteTag(tagId: string, token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/user-tag/${tagId}/`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(JSON.stringify(errorData), response.status);
            }
            
            const response_json = await response.json();
            const res_status = response.status;
            
            return {
                res_status: res_status,
                res_msg: response_json.message
            };
        } catch (error) {
            console.error('Error deleting tag:', error);
            return {
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to delete tag'
            };
        }
    }
    
    async updateTag(tagId: string, tagData: { name: string }, token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/user-tag/${tagId}/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(tagData)
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(JSON.stringify(errorData), response.status);
            }
            
            const response_json = await response.json();
            const res_status = response.status;
            
            return {
                tag: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };
        } catch (error) {
            console.error('Error updating tag:', error);
            return {
                tag: null,
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to update tag'
            };
        }
    }

    async assignTagsById(userId: string, tagData: { tag_ids: (string | number)[] }, token: string): Promise<UserResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/users/${userId}/tags/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(tagData)
            });
    
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }
    
            const response_json = await response.json();
            const res_status = response.status;
    
            console.info(`assignTagsById method - ${response_json.message}`);
            return {
                users: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };
    
        } catch (error) {
            console.error('Error assigning tags to user:', error);
            return {
                users: [],
                res_status: error.status,
                error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to assign tags to user'
            };
        }
    }

    // ========================= Filter List Methods =========================
    async getFilterPartners(token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/filters/partners/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(JSON.stringify(errorData), response.status);
            }
            
            const response_json = await response.json();
            const res_status = response.status;
            
            return {
                partners: response_json.partners,
                res_status: res_status
            };
        } catch (error) {
            console.error('Error fetching filter partners:', error);
            return {
                partners: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch filter partners'
            };
        }
    }

    async getFilterDepartments(token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/filters/departments/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(JSON.stringify(errorData), response.status);
            }
            
            const response_json = await response.json();
            const res_status = response.status;
            
            return {
                departments: response_json.departments,
                res_status: res_status
            };
        } catch (error) {
            console.error('Error fetching filter departments:', error);
            return {
                departments: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch filter departments'
            };
        }
    }

    async getFilterSpecializedTags(token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/filters/specialized-tags/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(JSON.stringify(errorData), response.status);
            }
            
            const response_json = await response.json();
            const res_status = response.status;
            
            return {
                specialized_tags: response_json.specialized_tags,
                res_status: res_status
            };
        } catch (error) {
            console.error('Error fetching filter specialized tags:', error);
            return {
                specialized_tags: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch filter specialized tags'
            };
        }
    }

    async getUsersWithFiltersAndOrdering(
        token: string, 
        filters: {
            search?: string;
            status?: string;
            role?: string;
            partner?: string;
            department?: string;
            user_tag?: string;
            page?: number;
            page_size?: number;
        } = {},
        ordering: string = 'id'
    ): Promise<UserResponse> {
        try {
            // Build query parameters
            const params = new URLSearchParams();
            
            // Add ordering (always send ordering parameter to ensure consistency)
            if (ordering) {
                params.append('ordering', ordering);
            }   

            // Add filters - only append if they have values
            if (filters.search && filters.search.trim()) {
                params.append('search', filters.search.trim());
            }
            if (filters.status && filters.status.trim()) {
                params.append('status', filters.status.trim());
            }
            if (filters.role && filters.role.trim()) {
                params.append('role', filters.role.trim());
            }
            if (filters.partner && filters.partner.trim()) {
                params.append('partner', filters.partner.trim());
            }
            if (filters.department && filters.department.trim()) {
                params.append('department', filters.department.trim());
            }
            if (filters.user_tag && filters.user_tag.trim()) {
                params.append('user_tag', filters.user_tag.trim());
            }
            if (filters.page && filters.page > 1) {
                params.append('page', filters.page.toString());
            }
            if (filters.page_size && filters.page_size > 0) {
                params.append('page_size', filters.page_size.toString());
            }

            const queryString = params.toString();
            const url = `${this.baseUrl}/api/users/paginated/${queryString ? '?' + queryString : ''}`;
            
            // Log the URL being requested
            console.log('=== USER API REQUEST ===');
            console.log('Full URL:', url);
            console.log('Query params:', {
                ordering,
                search: filters.search || '',
                status: filters.status || '',
                role: filters.role || '', 
                partner: filters.partner || '',
                department: filters.department || '',
                user_tag: filters.user_tag || '',
                page: filters.page || 1,
                page_size: filters.page_size || 10
            });
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    errorData.message || 'Failed to fetch users',
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                users: response_json,
                total_count: response_json.count || 0,
                total_pages: Math.ceil((response_json.count || 0) / (filters.page_size || 10)),
                current_page: filters.page || 1,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching users:', error);
            return {
                users: { results: [], count: 0 },
                res_status: error instanceof ApiError ? error.statusCode : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch users'
            };
        }
    }
    

    // async verifyToken(token: string): Promise<UserResponse> {
    //     try {
    //         const response = await fetch(`${this.baseUrl}/jwt/verify/`, {
    //             method: 'GET',
    //             headers: {
    //                 'Authorization': `Bearer ${token}`,
    //                 'Content-Type': 'application/json'
    //             }
    //         });

    //         if (!response.ok) {
    //             const errorData = await response.json();
    //             throw new ApiError(
    //                 JSON.stringify(errorData),
    //                 response.status
    //             );
    //         }


    //         const response_json = await response.json();
    //         const res_status = response.status;

    //         return {
    //             users: response_json,
    //             res_status: res_status
    //         };

    //     } catch (error) {
    //         console.error('Error fetching users:', error);
    //         return {
    //             users: [],
    //             res_status: error.status,
    //             error_msg: error instanceof Error ? error.message : 'Failed to fetch users'
    //         };
    //     }
    // }

    // async assignCompanyById(id: string, partnerData: { company_ids: (string | number)[] }, token: string): Promise<UserResponse> {
    //     try {
    //         // const response = await fetch(`${this.baseUrl}/api/users/${id}/companies/assign/`, {
    //         const response = await fetch(`${this.baseUrl}/api/users/${id}/partners/`, {
    //             method: 'PUT',
    //             headers: {
    //                 'Authorization': `Bearer ${token}`,
    //                 'Content-Type': 'application/json'
    //             },
    //             body: JSON.stringify(partnerData)
    //         });

    //         if (!response.ok) {
    //             const errorData = await response.json();
    //             throw new ApiError(
    //                 JSON.stringify(errorData),
    //                 response.status
    //             );
    //         }

    //         const response_json = await response.json();
    //         const res_status = response.status;
            
    //         console.info(`user's assignCompanyById method - ${response_json.message}`);
    //         return { 
    //             users: response_json.data, 
    //             res_status: res_status,
    //             res_msg: response_json.message
    //         };

    //     } catch (error) {
    //         console.error('Error updating user:', error);
    //         return {
    //             users: [],
    //             res_status: error.status,
    //             error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to updating user'
    //         };
    //     }
    // }

    async forceChangePassword(userId: string, passwordData: { new_password: string, confirm_password: string}, token: string): Promise<UserResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/users/${userId}/change-password/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(passwordData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                users: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };

        } catch (error) {
            console.error('Error changing user password:', error);
            return {
                users: [],
                res_status: error instanceof ApiError ? error.status : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to change user password'
            };
        }
    }


    async changePassword(userId: string, passwordData: { old_password: string, new_password: string, confirm_password: string}, token: string): Promise<UserResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/my/change-password/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(passwordData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                users: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };

        } catch (error) {
            console.error('Error changing user password:', error);
            return {
                users: [],
                res_status: error instanceof ApiError ? error.status : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to change user password'
            };
        }
    }

}