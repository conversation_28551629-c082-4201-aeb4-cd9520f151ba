<script lang="ts" context="module">
	import { toastStore } from '$lib/stores/toastStore';

	interface EnhanceOptions {
		modalOpen: boolean;
		setModalOpen?: (value: boolean) => void;
		setPending?: (value: boolean) => void;
		setShowSuccessMessage?: (value: boolean) => void;
		setSuccessMessage?: (value: string) => void;
		setShowErrorMessage?: (value: boolean) => void;
		setErrorMessage?: (value: string) => void;
		// New optional properties for enhanced behavior
		useToastOnSuccess?: boolean;
		useToastOnError?: boolean;
		closeModalOnSuccess?: boolean;
		onSuccess?: () => void; // Callback for successful operations
	}

	export const handleEnhance = (options: EnhanceOptions) => {
		return async ({ result, update }: { result: any; update: () => Promise<void> }) => {
			// TODO - Delete this
			// console.log(`src\lib\components\Enhance\SubmissionHandleEnhance.svelte's result - ${JSON.stringify(result)}`)
			// console.log(`src\lib\components\Enhance\SubmissionHandleEnhance.svelte's result type - ${result.type}`)

			// Set pending state if the option is provided
			options.setPending?.(true);

			if (result.type === 'failure') {
				const errorMessage = result.data?.error || 'Status : Operation failed';
				
				if (options.useToastOnError) {
					toastStore.add(errorMessage, 'error');
				} else {
					// options.setShowErrorMessage?.(true);
					// options.setErrorMessage?.(errorMessage);
				}
				// Don't close modal on error
			} else if (result.type === 'success') {
				console.log(result);
				console.log(result.data);
				const successMessage = result.data?.message || 'Status : Operation success';

				if (options.useToastOnSuccess) {
					toastStore.add(successMessage, 'success');
				} else {
					// options.setShowSuccessMessage?.(true);
					// options.setSuccessMessage?.(successMessage);
				}

				if (options.closeModalOnSuccess) {
					options.setModalOpen?.(false);
				}

				// Call the success callback if provided
				options.onSuccess?.();
			}
			
			// Only update page data if no onSuccess callback is provided
			// If onSuccess is provided, it handles the data refresh itself
			if (!options.onSuccess) {
				await update();
			}

			// Reset pending state if the option is provided
			options.setPending?.(false);
		};
	};
</script>
