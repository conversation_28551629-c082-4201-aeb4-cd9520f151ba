// Customer related types

export interface Customer {
    customer_id: number;
    universal_id?: string;
    
    // Personal Information
    first_name?: string;
    last_name?: string;
    middle_name?: string;
    name?: string;
    nickname?: string;
    title?: string;
    
    // Demographics
    date_of_birth?: string;
    age?: number;
    gender_id?: number;
    nationality?: string;
    
    // Contact Information
    email?: string;
    email_verified?: boolean;
    email_verified_date?: string;
    phone?: string;
    phone_verified?: boolean;
    phone_verified_date?: string;
    
    // Address
    address_line1?: string;
    address_line2?: string;
    subdistrict?: string;
    district?: string;
    province?: string;
    postal_code?: string;
    country?: string;
    
    // Professional Information
    career?: string;
    occupation?: string;
    company_name?: string;
    industry?: string;
    annual_income_range?: string;
    
    // Preferences
    preferred_language?: string;
    preferred_contact_method?: string;
    preferred_contact_time?: string;
    accepts_marketing?: boolean;
    accepts_sms?: boolean;
    accepts_email?: boolean;
    accepts_push_notifications?: boolean;
    
    // Customer Relationship
    customer_type: 'PROSPECT' | 'NEW' | 'REGULAR' | 'VIP' | 'INACTIVE';
    customer_segment?: string;
    lifetime_value?: number;
    referral_source?: string;
    referred_by?: number;
    referral_code?: string;
    
    // Account Status
    account_status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'BLACKLISTED' | 'DELETED';
    risk_level?: string;
    kyc_status?: string;
    kyc_verified_date?: string;
    
    // Activity Tracking
    first_contact_date?: string;
    last_contact_date?: string;
    last_purchase_date?: string;
    total_purchases?: number;
    total_spent?: number;
    
    // Platform Specific
    main_interface_id?: number;
    platform_identities?: CustomerPlatformIdentity[];
    
    // Tags and Notes
    customer_tags?: CustomerTag[];
    tags?: CustomerTag[];
    notes?: string;
    custom_fields?: any;
    
    // UI/Display fields
    platforms?: PlatformBadge[];
    last_message_time?: number;
    open_tickets?: number;
    total_messages?: number;
    unread_count?: number;
    latest_ticket_status?: string;
    
    // Metadata
    created_by?: number;
    created_on?: string;
    updated_by?: number;
    updated_on?: string;
}

export interface CustomerPlatformIdentity {
    id: number;
    customer: number;
    platform: 'LINE' | 'WHATSAPP' | 'FACEBOOK' | 'TELEGRAM' | 'INSTAGRAM';
    platform_user_id: string;
    provider_id?: string;
    provider_name?: string;
    channel_id?: string;
    channel_name?: string;
    display_name?: string;
    picture_url?: string;
    status_message?: string;
    platform_data?: any;
    is_active: boolean;
    is_verified: boolean;
    last_interaction?: string;
    created_on?: string;
    unread_count?: number;
}

// export interface Message {
//     id: number;
//     ticket_id: number;
//     message: string;
//     user_name: string;
//     is_self: boolean;
//     message_type: 'TEXT' | 'IMAGE' | 'FILE' | 'AUDIO' | 'VIDEO';
//     status: 'SENDING' | 'SENT' | 'DELIVERED' | 'READ' | 'FAILED';
//     created_on: string;
//     delivered_on?: string;
//     read_on?: string;
//     file_url?: string;
//     metadata?: any;
//     platform_identity?: number;
//     platform_identity_id?: number;
//     user_image_url?: string;
//     is_cross_channel?: boolean;
//     source_channel?: string;
// }

export interface MessageFile {
    name: string;
    size: number;
    type: string;
    uploaded_at: string;
    blob_path?: string;
}

export interface MessageFileMetadata {
    files: MessageFile[];
    total_files: number;
    total_size: number;
    uploaded_at: string;
}

export interface Message {
    id: number;
    ticket_id: number;
    message: string;
    user_name: string;
    is_self: boolean;
    message_type: 'TEXT' | 'IMAGE' | 'FILE' | 'TEXT_FILE' | 'AUDIO' | 'VIDEO';
    status: 'SENT' | 'DELIVERED' | 'READ' | 'FAILED';
    created_on: string;
    updated_on?: string;
    platform_identity?: number;
    created_by?: number;
    
    // File-related fields
    file_url?: string[];
    file_metadata?: MessageFileMetadata;
    has_attachments?: boolean;
    attachment_count?: number;
    total_file_size?: number;
    files?: MessageFile[];  // Processed from file_metadata
    
    // UI helpers
    user_image_url?: string;
}

export interface CustomerTag {
    id: number;
    name: string;
    description?: string;
    color?: string;
}

export interface PlatformBadge {
    platform: string;
    verified: boolean;
    last_interaction?: string;
    status?: string;
}

export interface CustomerStats {
    customer_id: number;
    total_tickets: number;
    open_tickets: number;
    closed_tickets: number;
    total_messages: number;
    avg_response_time?: string;
    last_24h_messages: number;
    last_7d_messages: number;
    last_30d_messages: number;
    customer_since?: string;
    lifetime_value?: number;
    active_platforms: string[];
}

export interface LinkingCodeResponse {
    success: boolean;
    code: string;
    expires_at: string;
    expires_in_hours: number;
}

export interface PaginatedResponse<T> {
    results: T[];
    count: number;
    next?: string;
    previous?: string;
    page?: number;
    page_size?: number;
    has_more?: boolean;
}

export interface CustomerFilters {
    search?: string;
    platform?: string;
    hasOpenTickets?: boolean;
    page?: number;
    pageSize?: number;
}

export interface CustomerLinkingHistory {
    id: number;
    primary_customer: number;
    linked_customer?: number;
    linking_method: 'CODE' | 'EMAIL' | 'PHONE' | 'MANUAL' | 'AUTO';
    platform_identity?: number;
    status: 'PENDING' | 'SUCCESS' | 'FAILED' | 'EXPIRED';
    linking_code_used?: string;
    failure_reason?: string;
    metadata?: any;
    created_on: string;
    completed_on?: string;
}

export interface CustomerNote {
    id: number;
    customer: number;
    content: string;
    is_active: boolean;
    created_by?: number;
    created_on: string;
    updated_by?: number;
    updated_on: string;
}

export interface CustomerMemory {
    id: number;
    customer: number;
    entity_one: string;
    entity_two: string;
    relation_type: string;
    is_important: boolean;
    detail_en: string;
    detail_th: string;
    ticket?: number;
    created_by?: number;
    created_on: string;
    updated_by?: number;
    updated_on: string;
}

// WebSocket event types
export interface CustomerUpdateEvent {
    type: 'customer_update';
    customer: Customer;
}

export interface PlatformStatusUpdateEvent {
    type: 'platform_status_update';
    platform_identity_id: number;
    platform: string;
    status: string;
    channel_name?: string;
}

export interface NewMessageNotificationEvent {
    type: 'new_message_notification';
    customer_id: number;
    platform: string;
    platform_identity_id: number;
    message_preview?: string;
    timestamp: string;
}

export interface MessageStatusUpdateEvent {
    type: 'message_status_update';
    message_id: number;
    status: string;
    timestamp?: string;
}

export interface TypingIndicatorEvent {
    type: 'typing_indicator';
    platform_identity_id: number;
    is_typing: boolean;
    user_name?: string;
}